#!/usr/bin/env python3
"""
Mathews Mailwizz Unsubscribers Processor

Professional script to process directories in "H:\<PERSON> Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs"
and compile email data from CSV files in each folder, saving output files with folder name + "_mwz_unsubs" 
in a "compiled unsubs" directory.

Based on email_col.py pattern but enhanced for directory processing.

Author: AI Assistant
Date: 2025-08-22
"""

import os
import glob
import pandas as pd
import numpy as np
import warnings
from datetime import datetime
from pathlib import Path

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# Configuration
BASE_PATH = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs"
OUTPUT_FOLDER_NAME = "compiled unsubs"

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 60, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def get_subdirectories(base_path):
    """
    Get all subdirectories in the base path.
    
    Args:
        base_path (str): The base directory path
        
    Returns:
        list: List of subdirectory paths
    """
    subdirs = []
    try:
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                subdirs.append(item_path)
        return sorted(subdirs)
    except FileNotFoundError:
        rich_progress.print_status(f"Base path not found: {base_path}", "error")
        return []
    except Exception as e:
        rich_progress.print_status(f"Error accessing base path: {e}", "error")
        return []

def process_csv_files_in_directory(directory_path):
    """
    Process all CSV files in a directory and extract emails.
    
    Args:
        directory_path (str): Path to the directory containing CSV files
        
    Returns:
        pandas.DataFrame: DataFrame containing all unique emails
    """
    # Change to the directory
    original_cwd = os.getcwd()
    
    try:
        os.chdir(directory_path)
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            rich_progress.print_status(f"No CSV files found in {os.path.basename(directory_path)}", "warning")
            return pd.DataFrame(columns=['Email'])
        
        all_emails = pd.DataFrame(columns=['Email'])
        processed_files = 0
        skipped_files = 0
        
        for f in csv_files:
            try:
                df = pd.read_csv(f, on_bad_lines='skip', low_memory=False)
                
                # Check for 'Email' or 'email' header (case-insensitive)
                email_column = None
                for col in df.columns:
                    if col.lower() == 'email':
                        email_column = col
                        break
                
                if email_column is None:
                    rich_progress.print_status(f"Warning: File {f} does not contain 'Email' column. Skipping.", "warning")
                    skipped_files += 1
                    continue

                # Extract emails
                emails = df[email_column]
                
                # Create a temporary DataFrame to hold the emails
                temp_df = pd.DataFrame({'Email': emails})
                
                # Concatenate to the main DataFrame
                all_emails = pd.concat([all_emails, temp_df], ignore_index=True)
                processed_files += 1

            except pd.errors.EmptyDataError:
                rich_progress.print_status(f"Warning: File {f} is empty. Skipping.", "warning")
                skipped_files += 1
            except pd.errors.ParserError:
                rich_progress.print_status(f"Warning: File {f} has parsing errors. Skipping.", "warning")
                skipped_files += 1
            except Exception as e:
                rich_progress.print_status(f"Error processing file {f}: {e}", "error")
                skipped_files += 1

        # Remove duplicates and NaN values
        initial_count = len(all_emails)
        all_emails.drop_duplicates(subset='Email', inplace=True)
        all_emails.dropna(subset='Email', inplace=True)
        final_count = len(all_emails)
        
        duplicates_removed = initial_count - final_count
        
        rich_progress.print_status(f"Processed {processed_files} files, skipped {skipped_files} files", "info")
        rich_progress.print_status(f"Extracted {final_count} unique emails (removed {duplicates_removed} duplicates)", "success")
        
        return all_emails
        
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

def create_output_directory(base_path):
    """
    Create the output directory for compiled unsubs.
    
    Args:
        base_path (str): Base path where to create the output directory
        
    Returns:
        str: Path to the created output directory
    """
    output_dir = os.path.join(base_path, OUTPUT_FOLDER_NAME)
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def main():
    """Main function to process all directories."""
    
    # Print welcome header
    print_header("Mathews Mailwizz Unsubscribers Processor")
    
    # Validate base path
    print_section("Validating Base Path")
    if not os.path.exists(BASE_PATH):
        rich_progress.print_status(f"Base path does not exist: {BASE_PATH}", "error")
        rich_progress.print_status("Please check the path and try again.", "error")
        return
    
    rich_progress.print_status(f"Base path validated: {BASE_PATH}", "success")
    
    # Get all subdirectories
    print_section("Discovering Subdirectories")
    subdirectories = get_subdirectories(BASE_PATH)
    
    if not subdirectories:
        rich_progress.print_status("No subdirectories found to process.", "warning")
        return
    
    rich_progress.print_status(f"Found {len(subdirectories)} subdirectories to process:", "success")
    for i, subdir in enumerate(subdirectories, 1):
        rich_progress.print_status(f"  {i}. {os.path.basename(subdir)}", "info")
    
    # Create output directory
    print_section("Creating Output Directory")
    output_dir = create_output_directory(BASE_PATH)
    rich_progress.print_status(f"Output directory: {output_dir}", "success")
    
    # Process each subdirectory
    print_section("Processing Subdirectories")
    
    # Create progress bar for directories
    dir_bar, update_dir = rich_progress.create_progress_bar(
        total=len(subdirectories),
        description="Processing directories",
        color_scheme="green"
    )
    
    processed_count = 0
    error_count = 0
    total_emails = 0
    
    for subdir in subdirectories:
        folder_name = os.path.basename(subdir)
        update_dir(0, f"Processing {folder_name}")
        
        try:
            # Process CSV files in this directory
            emails_df = process_csv_files_in_directory(subdir)
            
            if len(emails_df) > 0:
                # Create output filename
                output_filename = f"{folder_name}_mwz_unsubs.csv"
                output_path = os.path.join(output_dir, output_filename)
                
                # Save the emails to CSV file
                emails_df['Email'].to_csv(output_path, index=False, encoding='utf-8-sig')
                
                rich_progress.print_status(f"✓ Saved {len(emails_df)} emails to {output_filename}", "success")
                processed_count += 1
                total_emails += len(emails_df)
            else:
                rich_progress.print_status(f"✗ No emails found in {folder_name}", "warning")
            
            update_dir(1, f"Completed {folder_name}")
            
        except Exception as e:
            rich_progress.print_status(f"✗ Error processing {folder_name}: {e}", "error")
            error_count += 1
            update_dir(1, f"Error with {folder_name}")
    
    # Stop progress bar
    dir_bar.stop()
    
    # Print completion summary
    print_header("Processing Completed!")
    rich_progress.print_status(f"Directories processed successfully: {processed_count}", "success")
    rich_progress.print_status(f"Directories with errors: {error_count}", "error" if error_count > 0 else "info")
    rich_progress.print_status(f"Total unique emails compiled: {total_emails:,}", "success")
    rich_progress.print_status(f"Output directory: {output_dir}", "info")
    
    # List generated files
    if processed_count > 0:
        print_section("Generated Files")
        output_files = glob.glob(os.path.join(output_dir, "*_mwz_unsubs.csv"))
        for i, file_path in enumerate(sorted(output_files), 1):
            filename = os.path.basename(file_path)
            try:
                # Count rows in the file
                df_count = pd.read_csv(file_path)
                count = len(df_count)
                rich_progress.print_status(f"  {i}. {filename} ({count:,} emails)", "info")
            except:
                rich_progress.print_status(f"  {i}. {filename}", "info")

if __name__ == "__main__":
    main()
