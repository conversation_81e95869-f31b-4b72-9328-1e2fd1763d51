#!/usr/bin/env python3
"""
Custom Countries Data Separator Script

Separates CSV data by specific countries based on email domains.
Based on the pattern from hic_all_countries.py but customized for specific countries.

Countries included:
Argentina, Australia, Austria, Belgium, Botswana, Brazil, Bulgaria, Canada, Chile, Croatia,
Hong Kong, Iceland, Israel, Japan, Kuwait, Malaysia, Mexico, New Zealand, Norway, Philippines,
South Korea, Switzerland, Taiwan, Thailand, Uganda, United Arab Emirates, United Kingdom,
United States, Vietnam

Author: AI Assistant
Date: 2025-08-22
"""

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# TLD to Country Name Mapping for specified countries
target_countries_mapping = {
    '.ar': 'Argentina',
    '.au': 'Australia', 
    '.at': 'Austria',
    '.be': 'Belgium',
    '.bw': 'Botswana',
    '.br': 'Brazil',
    '.bg': 'Bulgaria',
    '.ca': 'Canada',
    '.cl': 'Chile',
    '.hr': 'Croatia',
    '.hk': 'Hong Kong',
    '.is': 'Iceland',
    '.il': 'Israel',
    '.jp': 'Japan',
    '.kw': 'Kuwait',
    '.my': 'Malaysia',
    '.mx': 'Mexico',
    '.nz': 'New Zealand',
    '.no': 'Norway',
    '.ph': 'Philippines',
    '.kr': 'South Korea',
    '.ch': 'Switzerland',
    '.tw': 'Taiwan',
    '.th': 'Thailand',
    '.ug': 'Uganda',
    '.ae': 'United Arab Emirates',
    '.uk': 'United Kingdom',
    '.us': 'United States',
    '.vn': 'Vietnam'
}

# Special domains for certain countries
special_domains = {
    # US special domains
    '.edu': 'United States',
    '.gov': 'United States', 
    '.mil': 'United States'
}

def extract_segment_from_path(path):
    """
    Extract conference segment name from path.

    Args:
        path (str): The file path to extract segment from

    Returns:
        str or None: The extracted segment name or None if not found
    """
    pattern = r"\\([\w\s-]+\d{4})\\?"
    match = re.search(pattern, path)
    if match:
        segment = match.group(1)
        rich_progress.print_status(f"Found segment: {segment}", "success")
        return segment
    else:
        rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
        # Prompt for manual input
        segment = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
        if segment.strip():
            rich_progress.print_status(f"Using manually entered segment: {segment}", "info")
            return segment
        else:
            rich_progress.print_status("No segment name provided", "error")
            return None

def extract_country_from_email(email):
    """Extract country name from email address based on domain."""
    if pd.isna(email):
        return 'Unknown'

    email = str(email).lower()

    # Check for target countries TLDs
    for tld, country in target_countries_mapping.items():
        if email.endswith(tld):
            return country
    
    # Check for special domains
    for domain, country in special_domains.items():
        if email.endswith(domain):
            return country

    # If no match found, return 'Other'
    return 'Other'

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("Custom Countries Email Domain Filtering Tool")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")
csn = extract_segment_from_path(path)
if not csn:
    rich_progress.print_status("No segment name provided. Exiting.", "error")
    exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read and concatenate CSV files with progress bar
print_section("Reading CSV Files")
rich_progress.print_status(f"Reading {len(csv_files)} CSV files...", "info")

# Create a progress bar for reading CSV files
read_bar, update_read = rich_progress.create_progress_bar(
    total=len(csv_files),
    description="Reading CSV files",
    color_scheme="blue"
)

# Read each CSV file with progress tracking
dfs = []
for csv_file in csv_files:
    try:
        df = pd.read_csv(csv_file, low_memory=False)
        dfs.append(df)
        update_read(1, f"Read {csv_file}")
    except Exception as e:
        rich_progress.print_status(f"Error reading {csv_file}: {str(e)}", "error")
        update_read(1, f"Error with {csv_file}")

# Stop the progress bar
read_bar.stop()

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

# Add Country column
print_section("Adding Country Column")
rich_progress.print_status("Extracting country information from email domains...", "info")
d2_EVENT['Country'] = d2_EVENT['Email'].apply(extract_country_from_email)
rich_progress.print_status(f"Successfully added Country column to {len(d2_EVENT)} records", "success")

# Create filters for each target country
print_section("Creating Country Filters")
rich_progress.print_status("Setting up filters for target countries...", "info")

# Create a dictionary to store country dataframes
country_dfs = {}

# Create a progress bar for filtering
filter_bar, update_filter = rich_progress.create_progress_bar(
    total=len(target_countries_mapping) + len(special_domains),
    description="Creating country filters",
    color_scheme="green"
)

# Filter for each target country TLD
for tld, country_name in target_countries_mapping.items():
    country_filter = d2_EVENT["Email"].str.endswith(tld, na=False)
    country_dfs[country_name] = d2_EVENT[country_filter]
    update_filter(1, f"Filtered {country_name}")

# Filter for special domains (US)
for domain, country_name in special_domains.items():
    if country_name not in country_dfs:
        country_dfs[country_name] = d2_EVENT[d2_EVENT["Email"].str.endswith(domain, na=False)]
    else:
        # Combine with existing filter for US
        additional_filter = d2_EVENT["Email"].str.endswith(domain, na=False)
        country_dfs[country_name] = pd.concat([country_dfs[country_name], d2_EVENT[additional_filter]], ignore_index=True).drop_duplicates()
    update_filter(1, f"Added {domain} to {country_name}")

# Stop the progress bar
filter_bar.stop()

# Create "Others" filter for data that doesn't belong to target countries
print_section("Creating Others Filter")
rich_progress.print_status("Filtering data for others category...", "info")

# Combine all target country filters
all_target_filters = pd.Series(False, index=d2_EVENT.index)

# Add target country TLD filters
for tld in target_countries_mapping.keys():
    all_target_filters = all_target_filters | d2_EVENT["Email"].str.endswith(tld, na=False)

# Add special domain filters
for domain in special_domains.keys():
    all_target_filters = all_target_filters | d2_EVENT["Email"].str.endswith(domain, na=False)

# Create Others filter (everything NOT in target countries)
others_filter = ~all_target_filters
df_others = d2_EVENT[others_filter]
rich_progress.print_status(f"Others category: {len(df_others)} records", "success")

# Print filtering results
print_section("Filtering Results")
rich_progress.print_status("Country filtering complete. Results:", "success")
rich_progress.print_status("-" * 50, "info")
rich_progress.print_status(f"{'Country':<25} {'Count':>10}", "info")
rich_progress.print_status("-" * 50, "info")

# Print counts for each target country (sorted by count)
country_counts = [(country, len(df)) for country, df in country_dfs.items()]
country_counts.sort(key=lambda x: x[1], reverse=True)

for country, count in country_counts:
    rich_progress.print_status(f"{country:<25} {count:>10}", "info")

rich_progress.print_status(f"{'Others':<25} {len(df_others):>10}", "info")
rich_progress.print_status("-" * 50, "info")

# Print country distribution from the Country column
print_section("Country Distribution Summary")
country_distribution = d2_EVENT['Country'].value_counts()
rich_progress.print_status(f"Total unique countries identified: {len(country_distribution)}", "info")
rich_progress.print_status(f"Target countries found: {len([c for c in country_distribution.index if c in target_countries_mapping.values() or c in special_domains.values()])}", "info")
rich_progress.print_status(f"Other countries: {len([c for c in country_distribution.index if c not in target_countries_mapping.values() and c not in special_domains.values()])}", "info")

# Create output directories
print_section("Creating Output Directories")
countries_dir = os.path.join(os.getcwd(), "sorted")
others_dir = os.path.join(os.getcwd(), "sorted")

# Create directories
os.makedirs(countries_dir, exist_ok=True)
os.makedirs(others_dir, exist_ok=True)

rich_progress.print_status(f"Created output directory: {countries_dir}", "info")

# Prepare files to save
print_section("Preparing Output Files")
files_to_save = []

# Add country files (only for countries with records)
for country_name, df in country_dfs.items():
    if len(df) > 0:
        # Clean country name for filename (remove spaces, special characters)
        clean_country_name = country_name.replace(" ", "_").replace(".", "")
        filename = f"{csn}_{clean_country_name}.csv"
        files_to_save.append((df, filename, country_name, countries_dir))

# Add Others file
if len(df_others) > 0:
    files_to_save.append((df_others, f"{csn}_Others.csv", "Others", others_dir))

rich_progress.print_status(f"Prepared {len(files_to_save)} files for saving", "info")

# Save files with progress tracking
print_section("Saving Output Files")

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving files",
    color_scheme="purple"
)

# Save each file with progress tracking
saved_count = 0
error_count = 0

for df, filename, category, directory in files_to_save:
    try:
        output_path = os.path.join(directory, filename)
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        update_save(1, f"Saved {category} ({len(df)} records)")
        saved_count += 1
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")
        error_count += 1

# Stop the progress bar
save_bar.stop()

# Print completion message
print_header("Processing Completed Successfully!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")
rich_progress.print_status(f"Files successfully saved: {saved_count}", "success")
if error_count > 0:
    rich_progress.print_status(f"Files with errors: {error_count}", "error")

rich_progress.print_status(f"Output directory: {countries_dir}", "info")

# Print summary of saved files
print_section("Saved Files Summary")
rich_progress.print_status("-" * 60, "info")
rich_progress.print_status(f"{'Filename':<35} {'Records':>10} {'Country':>15}", "info")
rich_progress.print_status("-" * 60, "info")

for df, filename, category, directory in files_to_save:
    rich_progress.print_status(f"{filename:<35} {len(df):>10} {category:>15}", "info")

rich_progress.print_status("-" * 60, "info")
rich_progress.print_status("All files saved with utf-8-sig encoding in 'sorted' directory", "success")
